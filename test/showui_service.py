"""
ShowUI API 服务 - 全中文注释版
封装ShowUI功能为可重用的API服务，提供UI元素定位、导航动作生成和屏幕自动化功能。
增加了对高分屏（Retina）的适配，并将截图功能内置到定位函数中。
"""
import ast
import torch
import os
import time
from datetime import datetime
from PIL import Image, ImageDraw
from peft import PeftModel
from transformers import Qwen2VLForConditionalGeneration, AutoProcessor
from typing import Optional, Tuple, List, Dict, Any

try:
    import pyautogui
except ImportError:
    print("警告: pyautogui 库未安装。截图和鼠标点击功能将不可用。")
    print("请运行: pip install pyautogui")
    pyautogui = None


class ShowUIService:
    # ... (init, _load_model, _generate_text, _process_vision_info, _draw_point 等方法保持不变) ...
    # 这里为了简洁省略了未改动的方法，实际使用时请保留它们

    def __init__(self, model_path: str = "./ShowUI-2B", device: str = "mps"):
        """
        初始化ShowUI服务

        参数:
            model_path: 模型路径
            device: 设备类型 ("mps", "cuda", "cpu")
        """
        self.model_path = model_path
        self.device = device
        self.model = None
        self.processor = None
        self.min_pixels = 256 * 28 * 28
        self.max_pixels = 1344 * 28 * 28
        self._LOCATE_SYSTEM = ("Based on the screenshot of the page, I give a text description and you give its corresponding location. The coordinate represents a clickable location [x, y] for an element, which is a relative coordinate on the screenshot, scaled from 0 to 1.")
        self._load_model()

    def _load_model(self):
        """加载模型和处理器"""
        print(f"正在加载模型到 {self.device.upper()} 设备，这可能需要一些时间并且占用较多内存...")
        self.model = Qwen2VLForConditionalGeneration.from_pretrained(self.model_path, torch_dtype=torch.bfloat16, device_map=self.device)

        # 尝试加载微调后的LoRA权重
        try:
            print("正在尝试加载微调后的LoRA权重...")
            # 获取项目根目录的绝对路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)
            lora_path = os.path.join(project_root, "runs", "test_with_val", "2025-06-12_21-56-30", "ckpt_model")

            # 检查是否存在adapter_config.json文件
            adapter_config_path = os.path.join(lora_path, "adapter_config.json")
            if os.path.exists(adapter_config_path):
                self.model = PeftModel.from_pretrained(self.model, lora_path)
                print("成功加载微调后的LoRA权重。")
            else:
                print(f"警告: 未找到LoRA配置文件 {adapter_config_path}")
                print("将使用原始的ShowUI-2B模型，未应用微调权重。")
        except Exception as e:
            print(f"加载LoRA权重时出错: {str(e)}")
            print("将使用原始的ShowUI-2B模型，未应用微调权重。")

        self.processor = AutoProcessor.from_pretrained(self.model_path, size={"shortest_edge": self.min_pixels, "longest_edge": self.max_pixels}, use_fast=True)

        print("模型加载完成。")

    def _generate_text(self, messages: List[Dict], max_new_tokens: int = 128) -> str:
        text = self.processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
        image_inputs, _ = self._process_vision_info(messages)
        inputs = self.processor(text=[text], images=image_inputs, videos=None, padding=True, return_tensors="pt").to(self.device)
        generated_ids = self.model.generate(**inputs, max_new_tokens=max_new_tokens)
        generated_ids_trimmed = [out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)]
        return self.processor.batch_decode(generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False)[0]

    def _process_vision_info(self, messages: List[Dict]) -> Tuple[List[Image.Image], None]:
        image_inputs = []
        for msg in messages:
            if msg['role'] == 'user':
                for content in msg['content']:
                    if content['type'] == 'image':
                        img = Image.open(content['image'])
                        # 如果图像太大，调整大小以避免内存问题
                        max_size = 1024  # 最大宽度或高度，进一步减小以提高速度
                        if img.width > max_size or img.height > max_size:
                            if img.width > img.height:
                                new_width = max_size
                                new_height = int(img.height * max_size / img.width)
                            else:
                                new_height = max_size
                                new_width = int(img.width * max_size / img.height)
                            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                            print(f"图像已调整大小为: {new_width}x{new_height}")
                        image_inputs.append(img)
        return image_inputs, None

    def _draw_point(self, image_input, point=None, radius=10, save_path=None) -> Tuple[Image.Image, str]:
        if isinstance(image_input, str): image = Image.open(image_input).convert("RGBA")
        else: image = image_input.convert("RGBA")
        original_filename = os.path.basename(image_input) if isinstance(image_input, str) else "image"
        if point and isinstance(point, (list, tuple)) and len(point) == 2:
            overlay = Image.new("RGBA", image.size, (255, 255, 255, 0))
            draw = ImageDraw.Draw(overlay)
            x, y = point[0] * image.width, point[1] * image.height
            draw.ellipse((x - radius, y - radius, x + radius, y + radius), fill='#ADFF2F', outline='black', width=2)
            image = Image.alpha_composite(image, overlay)
        output_dir = "outputs"; os.makedirs(output_dir, exist_ok=True)
        if save_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            name_without_ext = os.path.splitext(original_filename)[0]
            save_path = os.path.join(output_dir, f"{name_without_ext}_annotated_point_{timestamp}.png")
        image.convert("RGB").save(save_path)
        return image, save_path

    def _take_screenshot(self) -> Optional[str]:
        """辅助函数：执行截图并返回文件路径"""
        if not pyautogui:
            print("错误: pyautogui 库未安装，无法截图。")
            return None

        screenshot_dir = "Boss_imgs"
        os.makedirs(screenshot_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        image_path = os.path.join(screenshot_dir, f"screenshot_{timestamp}.png")

        pyautogui.screenshot().save(image_path)
        print(f"截图已保存到: {image_path}")
        return image_path

    def locate_element(self, query: str, save_result: bool = True) -> Dict[str, Any]:
        """
        【已更新】自动截图并定位UI元素的单个点。

        参数:
            query: 查询描述。
            save_result: 是否保存标注结果。
        """
        image_path = self._take_screenshot()
        if not image_path:
            return {"success": False, "error": "截图失败"}

        try:
            messages = [{"role": "user", "content": [{"type": "text", "text": self._LOCATE_SYSTEM}, {"type": "image", "image": image_path}, {"type": "text", "text": query}]}]
            output_text = self._generate_text(messages)
            try:
                click_xy = ast.literal_eval(output_text)
                if not isinstance(click_xy, (list, tuple)) or len(click_xy) != 2: raise ValueError("坐标格式不正确")
                image = Image.open(image_path)
                # 重要：这里返回的 pixel 坐标是基于截图文件的物理像素坐标
                pixel_x, pixel_y = click_xy[0] * image.width, click_xy[1] * image.height
                result = {"success": True, "coordinates": {"relative": click_xy, "pixel": [int(pixel_x), int(pixel_y)]}, "query": query, "image_path": image_path, "raw_output": output_text}
                if save_result: _, saved_path = self._draw_point(image_path, click_xy, 15); result["annotated_image_path"] = saved_path
                return result
            except (ValueError, SyntaxError) as e: return {"success": False, "error": f"无法解析坐标: {str(e)}", "raw_output": output_text, "query": query, "image_path": image_path}
        except Exception as e: return {"success": False, "error": f"处理过程中出现错误: {str(e)}", "query": query, "image_path": image_path}

    def locate_iteratively(self, initial_query: str, refined_query: str, zoom_box_size: int = 200) -> Dict[str, Any]:
        """
        【已更新】自动截图并通过迭代进行精细定位，以解决坐标偏移问题。
        返回的坐标是物理像素坐标，需要在使用前进行缩放转换。

        参数:
            initial_query: 第一次粗略定位的查询。
            refined_query: 在放大区域内进行精确查询。
            zoom_box_size: 放大区域的像素尺寸。
        """
        # 步骤 0: 自动截图
        image_path = self._take_screenshot()
        if not image_path:
            return {"success": False, "error": "截图失败"}

        print(f"--- 步骤 1: 开始粗略定位，查询: '{initial_query}' ---")
        # 注意：这里我们直接调用内部逻辑，而不是再次调用会截图的 `locate_element`
        initial_result = self.locate_element(query=initial_query, save_result=False)

        if not initial_result.get("success"):
            print("粗略定位失败，无法继续。")
            return initial_result

        # 获取粗略定位的像素坐标
        rough_pixels = initial_result['coordinates']['pixel']
        print(f"粗略定位成功，物理像素坐标: {rough_pixels}")

        full_image = Image.open(image_path)
        half_zoom = zoom_box_size // 2

        left = max(0, rough_pixels[0] - half_zoom)
        top = max(0, rough_pixels[1] - half_zoom)
        right = min(full_image.width, rough_pixels[0] + half_zoom)
        bottom = min(full_image.height, rough_pixels[1] + half_zoom)

        cropped_image = full_image.crop((left, top, right, bottom))

        temp_dir = "temp_crops"; os.makedirs(temp_dir, exist_ok=True)
        cropped_image_path = os.path.join(temp_dir, f"crop_{datetime.now().strftime('%Y%m%d%H%M%S%f')}.png")
        cropped_image.save(cropped_image_path)

        print(f"--- 步骤 2: 在放大区域内进行精细定位，查询: '{refined_query}' ---")
        # 在裁剪图上执行定位
        refined_result = self.locate_element(query=refined_query, save_result=False) # 调用会重新截图，需要修改

        # 这是一个小问题：直接调用 self.locate_element 会导致再次截图。
        # 为了解决这个问题，我们需要一个不带截图的内部版本。我们重构一下。
        # (稍后在下面代码中进行重构)
        # 让我们先完成当前逻辑，然后修复它。

        # 修复后的逻辑将在下面展示...

    # ... 为了清晰，我们重构一下，将核心定位逻辑分离出来
    def _locate_from_image_path(self, image_path: str, query: str, save_result: bool = True) -> Dict[str, Any]:
        """这是一个内部方法，它从一个已有的图片路径进行定位，不截图。"""
        try:
            messages = [{"role": "user", "content": [{"type": "text", "text": self._LOCATE_SYSTEM}, {"type": "image", "image": image_path}, {"type": "text", "text": query}]}]
            output_text = self._generate_text(messages)
            try:
                click_xy = ast.literal_eval(output_text)
                if not isinstance(click_xy, (list, tuple)) or len(click_xy) != 2: raise ValueError("坐标格式不正确")
                image = Image.open(image_path)
                pixel_x, pixel_y = click_xy[0] * image.width, click_xy[1] * image.height
                result = {"success": True, "coordinates": {"relative": click_xy, "pixel": [int(pixel_x), int(pixel_y)]}, "query": query, "image_path": image_path, "raw_output": output_text}
                if save_result: _, saved_path = self._draw_point(image_path, click_xy, 15); result["annotated_image_path"] = saved_path
                return result
            except (ValueError, SyntaxError) as e: return {"success": False, "error": f"无法解析坐标: {str(e)}", "raw_output": output_text, "query": query, "image_path": image_path}
        except Exception as e: return {"success": False, "error": f"处理过程中出现错误: {str(e)}", "query": query, "image_path": image_path}

    # 现在重写 locate_element 和 locate_iteratively
    def locate_element(self, query: str, save_result: bool = True) -> Dict[str, Any]:
        image_path = self._take_screenshot()
        if not image_path: return {"success": False, "error": "截图失败"}
        return self._locate_from_image_path(image_path, query, save_result)

    def locate_iteratively(self, initial_query: str, refined_query: str, zoom_box_size: int = 200) -> Dict[str, Any]:
        # 步骤 0: 自动截图
        full_image_path = self._take_screenshot()
        if not full_image_path: return {"success": False, "error": "截图失败"}

        print(f"--- 步骤 1: 开始粗略定位，查询: '{initial_query}' ---")
        initial_result = self._locate_from_image_path(full_image_path, initial_query, save_result=False)

        if not initial_result.get("success"):
            print("粗略定位失败，无法继续。")
            return initial_result

        rough_pixels = initial_result['coordinates']['pixel']
        print(f"粗略定位成功，物理像素坐标: {rough_pixels}")

        full_image = Image.open(full_image_path)
        half_zoom = zoom_box_size // 2
        left, top = max(0, rough_pixels[0] - half_zoom), max(0, rough_pixels[1] - half_zoom)
        right, bottom = min(full_image.width, rough_pixels[0] + half_zoom), min(full_image.height, rough_pixels[1] + half_zoom)

        cropped_image = full_image.crop((left, top, right, bottom))

        temp_dir = "temp_crops"; os.makedirs(temp_dir, exist_ok=True)
        cropped_image_path = os.path.join(temp_dir, f"crop_{datetime.now().strftime('%Y%m%d%H%M%S%f')}.png")
        cropped_image.save(cropped_image_path)

        print(f"--- 步骤 2: 在放大区域内进行精细定位，查询: '{refined_query}' ---")
        refined_result = self._locate_from_image_path(cropped_image_path, refined_query, save_result=False)

        os.remove(cropped_image_path)

        if not refined_result.get("success"):
            print("精细定位失败。")
            return refined_result

        refined_relative = refined_result['coordinates']['relative']
        crop_width, crop_height = right - left, bottom - top

        final_pixel_x = int(left + refined_relative[0] * crop_width)
        final_pixel_y = int(top + refined_relative[1] * crop_height)
        final_pixels = [final_pixel_x, final_pixel_y]

        print(f"精细定位成功，最终物理像素坐标: {final_pixels}")

        _, saved_path = self._draw_point(full_image_path, [final_pixel_x / full_image.width, final_pixel_y / full_image.height], radius=15)

        return {
            "success": True,
            "coordinates": {"pixel": final_pixels},
            "annotated_image_path": saved_path,
            "image_path": full_image_path # 返回原始截图路径以供后续使用
        }


def _get_screen_scale_factor(screenshot_path: str) -> float:
    """自动检测屏幕缩放比例"""
    if not pyautogui: return 1.0
    with Image.open(screenshot_path) as img:
        physical_width, _ = img.size
    logical_width, _ = pyautogui.size()
    return round(physical_width / logical_width)

def create_showui_service(model_path: str = "./ShowUI-2B", device: str = "mps") -> ShowUIService:
    return ShowUIService(model_path, device)

if __name__ == "__main__":
    if not pyautogui:
        print("请先安装 pyautogui 库 (`pip install pyautogui`) 以运行测试。")
        exit()

    service = create_showui_service()
    print("\n" + "="*50)
    print("--- 运行带高分屏适配的精细定位测试 (内置截图) ---")
    print("\n!!! 警告: 此测试将自动控制您的鼠标进行点击 !!!")
    print("请在5秒内将鼠标移动到您想要操作的应用窗口。")
    print("!!! 安全提示: 若要强制停止程序，请快速将鼠标移动到屏幕的左上角。")
    try:
        input("准备好后，请按 Enter 键继续，或按 Ctrl+C 退出...")
    except (KeyboardInterrupt, EOFError):
        print("\n操作已取消。")
        exit()
    pyautogui.FAILSAFE = True
    initial_q = "带有未读消息红点的聊天列表项"
    refined_q = "红色圆点的正中心"

    # 1. 直接调用迭代定位，函数会自动截图
    result = service.locate_iteratively(initial_q, refined_q)
    print("\n--- 最终定位结果 ---")
    print(result)

    if result.get("success"):
        # 2. 获取截图路径和物理坐标
        image_path = result['image_path']
        physical_coords = result['coordinates']['pixel']

        # 3. 检测缩放比例
        scale_factor = _get_screen_scale_factor(image_path)
        print(f"\n检测到屏幕缩放比例为: {scale_factor}x")

        print(f"AI模型在截图中找到的物理像素坐标为: {physical_coords}")

        # 4. 转换坐标
        logical_coords = [int(c / scale_factor) for c in physical_coords]
        print(f"转换后用于点击的逻辑坐标为: {logical_coords}")

        print(f"请在 'outputs' 文件夹中查看标注图片: {result.get('annotated_image_path')}")

        # 5. 点击
        print(f"\n将在3秒后点击逻辑坐标 {logical_coords} ...")
        time.sleep(3)
        pyautogui.click(logical_coords[0], logical_coords[1])
        print("点击完成。")
    else:
        print("\n定位失败，无法点击。")
    print("\n高分屏适配测试完成。")
    print("="*50)