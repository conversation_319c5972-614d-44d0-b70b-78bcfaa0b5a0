"""
简单的ShowUI API测试脚本
演示如何快速使用ShowUI服务
"""
import os
from datetime import datetime

import pyautogui

from showui_service import create_showui_service, _get_screen_scale_factor


def main():
    screenshot_dir = "Boss_imgs"; os.makedirs(screenshot_dir, exist_ok=True)
    image_path = os.path.join(screenshot_dir, f"scaled_test_{datetime.now().strftime('%Y%m%d%H%M%S')}.png")
    pyautogui.screenshot().save(image_path)
    print(f"\n已截取当前屏幕用于测试: {image_path}")
    scale_factor = _get_screen_scale_factor(image_path)
    print(f"\n检测到屏幕缩放比例为: {scale_factor}x")

    # 创建服务实例 - 使用微调后的模型
    print("正在初始化ShowUI服务（使用微调后的模型）...")
    service = create_showui_service(model_path="./ShowUI-2B", device="mps")

    # 测试单个查询
    print("\n执行UI元素定位...")
    # result = service.locate_element(
    #     query="带有文字 全部职位的框"
    # )
    result = service.locate_element(
        "用户发来的未读消息"
    )

    coords = result['coordinates']['pixel']
    # 打印结果
    print(f"相对坐标: {result['coordinates']['relative']}")
    print(f"像素坐标: {result['coordinates']['pixel']}")
    print(f"标注图片: {result['annotated_image_path']}")


    pyautogui.click(coords[0]/scale_factor, coords[1]/scale_factor)
    pyautogui.click(coords[0]/scale_factor, coords[1]/scale_factor)

if __name__ == "__main__":
    main()
