#%%
from vllm import LLM, SamplingParams
from transformers import AutoProcessor
from vllm.multimodal.utils import fetch_image
from PIL import Image, ImageDraw
from io import BytesIO
import matplotlib.pyplot as plt
import requests
import ast

def display(image, figsize=(10, 20)):
    if isinstance(image, str):
        image = Image.open(BytesIO(requests.get(image).content)) if image.startswith('http') else Image.open(image)
    plt.figure(figsize=figsize)
    plt.imshow(image)
    plt.axis('off')
    plt.show()

min_pixels = 256*28*28
max_pixels = 1344*28*28
temperature=0.9
top_p=0.9
max_tokens=512
gpu_num=1 # 4 # allow multi-gpu inference

_SYSTEM = "Based on the screenshot of the page, I give a text description and you give its corresponding location. The coordinate represents a clickable location [x, y] for an element, which is a relative coordinate on the screenshot, scaled from 0 to 1."
prompt="Delete Carnation Breakfast EssentialsNutritional Drink (Pack of 8) from the cart."
image_url="examples/web_shopping.png"
display(image_url)
#%%
# Adapt from https://github.com/vllm-project/vllm/blob/main/examples/offline_inference/vision_language_multi_image.py
def inference(question, image_url: str):
    try:
        from qwen_vl_utils import process_vision_info
    except ModuleNotFoundError:
        print('WARNING: `qwen-vl-utils` not installed, input images will not '
              'be automatically resized. You can enable this functionality by '
              '`pip install qwen-vl-utils`.')
        process_vision_info = None

    model_name = "showlab/ShowUI-2B"

    llm = LLM(
        model=model_name,
        max_model_len=32768 if process_vision_info is None else 4096,
        tensor_parallel_size=gpu_num,
        max_num_seqs=5,
        limit_mm_per_prompt={"image": 1},
    )

    placeholder = [{"type": "image", "image": image_url, "min_pixels": min_pixels, "max_pixels": max_pixels}]
    messages = [{
        "role": "system",
        "content": _SYSTEM
    }, {
        "role":
        "user",
        "content": [
            *placeholder,
            {
                "type": "text",
                "text": question
            },
        ],
    }]

    processor = AutoProcessor.from_pretrained(model_name)

    prompt = processor.apply_chat_template(messages,
                                           tokenize=False,
                                           add_generation_prompt=True)

    stop_token_ids = None

    if process_vision_info is None:
        image_data = fetch_image(image_url)
    else:
        image_data, _ = process_vision_info(messages)
        
    sampling_params = SamplingParams(
        temperature=temperature,
        top_p=top_p,
        max_tokens=max_tokens,
        stop_token_ids=stop_token_ids
    )

    outputs = llm.generate(
        {
            "prompt": prompt,
            "multi_modal_data": {
                "image": image_data
            },
        },
        sampling_params=sampling_params)
    
    return outputs

def draw_point(image_input, point=None, radius=5):
    if isinstance(image_input, str):
        image = Image.open(BytesIO(requests.get(image_input).content)) if image_input.startswith('http') else Image.open(image_input)
    else:
        image = image_input

    if point:
        x, y = point[0] * image.width, point[1] * image.height
        ImageDraw.Draw(image).ellipse((x - radius, y - radius, x + radius, y + radius), fill='red')
    display(image)
    return
#%%
outputs = inference(prompt, image_url)
output_text = outputs[0].outputs[0].text
print(output_text)
click_xy = ast.literal_eval(output_text)
draw_point(image_url, click_xy, 15)