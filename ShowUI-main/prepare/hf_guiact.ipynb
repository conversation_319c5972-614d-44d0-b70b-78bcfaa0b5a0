#%%
from PIL import Image, ImageDraw
import json
import os
import re
import pdb
import random
import argparse
import numpy as np
from collections import Counter
from IPython.display import display
from tqdm import tqdm
from data_utils import is_english_simple, bbox_2_point

def normalize_click(click, size):
    x, y = click
    width, height = size
    
    x_norm = x / width
    y_norm = y / height
    return [x_norm, y_norm]

def normalize_bbox(bbox, size):
    x1, y1, x2, y2 = bbox
    width, height = size
    
    x1_norm = x1 / width
    y1_norm = y1 / height
    x2_norm = x2 / width
    y2_norm = y2 / height
    return [x1_norm, y1_norm, x2_norm, y2_norm]

def draw_point_bbox(image_path, start=None, end=None, radius=5, line=3):
    image = Image.open(image_path)
    draw = ImageDraw.Draw(image)
    width, height = image.size
    
    if start is not None:
        if len(start) == 2:
            x, y = start[0] * width, start[1] * height
            draw.ellipse((x - radius, y - radius, x + radius, y + radius), fill='red', outline='red')
        elif len(start) == 4:
            x1, y1, x2, y2 = start[0] * width, start[1] * height, start[2] * width, start[3] * height
            draw.rectangle([x1, y1, x2, y2], outline='red', width=line)
    
    if end is not None:
        if len(end) == 2:
            x, y = end[0] * width, end[1] * height
            draw.ellipse((x - radius, y - radius, x + radius, y + radius), fill='blue', outline='blue')
        elif len(end) == 4:
            x1, y1, x2, y2 = end[0] * width, end[1] * height, end[2] * width, end[3] * height
            draw.rectangle([x1, y1, x2, y2], outline='blue', width=line)
    
    return image

def check_instruction(instruction):
    # if len(instruction) > 60 or instruction.strip() == '':
    #     return False
    # if ('{' in instruction) or ('}' in instruction):
    #     return False
    if not is_english_simple(instruction):
        return False
    return True 
#%%
# data_dir = '/blob/v-lqinghong/data/GUI_database/'
data_dir = '/home/<USER>/v-lqinghong/data/GUI_database/'

parser = argparse.ArgumentParser(description="Process data for pre-training.")
parser.add_argument("--web_imgs", default=f'{data_dir}/GUI_Course/GUIAct/images', help="Path to the directory containing web images.")
#%%
# web-single
args = parser.parse_args([])
args.stage = 'web-single'
args.split = 'train'
# args.split = 'test'

args.web_json = f'{data_dir}/GUI_Course/GUIAct/metadata/{args.stage}_{args.split}_data.json'
web_train = json.load(open(args.web_json, "r"))
#%%
total_step = []
total_i = 0
for _, x in enumerate(tqdm(web_train)):
    if isinstance(x, dict):
        act_list = x['actions_label']
        img_uid = x['image_id']
        img_url = os.path.join(args.web_imgs, img_uid+'.png')
        # image = Image.open(img_url)
        # img_size = image.size
        # img_array = np.array(image)
        # uniform = np.all(img_array == img_array[0, 0])
        # if uniform:
        #     print(img_uid)
        #     continue
        
        img_size = [x['image_size']['width'], x['image_size']['height']]
        
        step_history = []
        task = x['question']
        if not check_instruction(task):
            print(task)
            continue
        instruction = x['thoughts']
        
        # click - bbox
        # input - bbox + text
        x_act = []
        for i, xi in enumerate(act_list):
            action_type = xi['name'].lower()
            bbox = None
            point = None
            action_value = None
            
            if action_type in ['click', 'input', 'select', 'hover']:
                bbox_str = xi['element']['related']
                bbox_val = re.findall(r'\d+\.\d+', bbox_str)
                bbox = [float(x) for x in bbox_val]
                # bbox = normalize_bbox(bbox, img_size)
                point = bbox_2_point(bbox)
                # img_draw = draw_point_bbox(img_url, point)
                # print(task)
                # display(img_draw)

            if action_type in ['input', 'select', 'answer']:
                action_value = xi['text']

            if action_type in ['scroll']:
                down = float(xi['scroll']['related']['down'])
                right = float(xi['scroll']['related']['right'])
                if down > 0:
                    action_value = 'down'
                elif down < 0:
                    action_value = 'up'
                    
                if right > 0:
                    action_value = 'right'
                elif right < 0:
                    action_value = 'left'

            xi['action_type'] = action_type
            xi['action_value'] = action_value
            if action_value is not None and not check_instruction(action_value):
                continue
            xi['point'] = point
            x_act.append(xi)
            # xi['img_url'] = img_uid

        total_step.append({
            "id": "guiact_websingle_{}".format(total_i),
            "step_id": i,

            "task": task,
            "thought": instruction, 
            "img_url": img_uid,
            "img_size": img_size,

            "action_type": action_type,
            "action_value": action_value,
            "bbox": bbox,
            "point": point,
            
            "step": x_act,
            "step_history": step_history.copy(),
            })
        step_history.append(xi)
        total_i += 1
#%%
save_url = f"{data_dir}/GUI_Course/GUIAct/metadata/hf_{args.split}_{args.stage}.json"

with open(save_url, "w") as file:
    json.dump(total_step, file, indent=4)
#%%
# web-multi
args = parser.parse_args([])
args.stage = 'web-multi'
args.split = 'train'
# args.split = 'test'

args.web_json = f'{data_dir}/GUI_Course/GUIAct/metadata/{args.stage}_{args.split}_data.json'
web_train = json.load(open(args.web_json, "r"))
#%%
total_step = []
total_i = 0
for _, x in enumerate(tqdm(web_train)):
    if isinstance(x, dict):
        act_list = x['actions_label']
        img_uid = x['image_id']
        img_url = os.path.join(args.web_imgs, img_uid+'.png')
        # image = Image.open(img_url)
        # img_size = image.size
        # img_array = np.array(image)
        # uniform = np.all(img_array == img_array[0, 0])
        # if uniform:
        #     print(img_uid)
        #     continue
        
        img_size = [x['image_size']['width'], x['image_size']['height']]
        
        step_history = []
        task = x['question']
        instruction = x['thoughts']

        if not check_instruction(task):
            print(task)
            continue
        
        # click - bbox
        # select - [click1, click2]
        # input - bbox + text

        for i, xi in enumerate(act_list):
            action_type = xi['name'].lower()
            bbox = None
            point = None
            action_value = None
            
            if action_type in ['select_text']:
                start = xi['dual_point']['related']['from']
                start = re.findall(r'\d+\.\d+', start)
                start = [float(x) for x in start]
    
                end = xi['dual_point']['related']['to']
                end = re.findall(r'\d+\.\d+', end)
                end = [float(x) for x in end]
                point = [start, end]
            
            if action_type in ['click', 'input', 'select', 'hover']:
                bbox_str = xi['element']['related']
                bbox_val = re.findall(r'\d+\.\d+', bbox_str)
                bbox = [float(x) for x in bbox_val]
                # bbox = normalize_bbox(bbox, img_size)
                point = bbox_2_point(bbox)
                # img_draw = draw_point_bbox(img_url, point)
                # print(task)
                # display(img_draw)

            if action_type in ['input', 'select', 'answer', 'copy']:
                action_value = xi['text']

            if action_type in ['scroll']:
                down = float(xi['scroll']['related']['down'])
                right = float(xi['scroll']['related']['right'])
                if down > 0:
                    action_value = 'down'
                elif down < 0:
                    action_value = 'up'
                    
                if right > 0:
                    action_value = 'right'
                elif right < 0:
                    action_value = 'left'

            xi['action_type'] = action_type
            xi['action_value'] = action_value
            if action_value is not None and not check_instruction(action_value):
                continue
            
            xi['point'] = point

            total_step.append({
                "id": "guiact_websingle_{}".format(total_i),
                "step_id": i,

                "task": task,
                "thought": instruction, 
                "img_url": img_uid,
                "img_size": img_size,

                "action_type": action_type,
                "action_value": action_value,
                "bbox": bbox,
                "point": point,
                
                "step": xi,
                "step_history": step_history.copy(),
                })
            step_history.append(xi)
            total_i += 1
#%%
total_step = []
total_i = 0

task_dict = {}
for _, x in enumerate(tqdm(web_train)):
    if isinstance(x, dict):
        act_list = x['actions_label']
        img_uid = x['image_id']
        img_url = os.path.join(args.web_imgs, img_uid+'.png')
        # image = Image.open(img_url)
        # img_size = image.size
        # img_array = np.array(image)
        # uniform = np.all(img_array == img_array[0, 0])
        # if uniform:
        #     print(img_uid)
        #     continue

        task_id = '_'.join(img_uid.split('_')[:3])

        if task_id not in task_dict:
            task_dict[task_id] = []
        task_history = task_dict[task_id]
        
        img_size = [x['image_size']['width'], x['image_size']['height']]
        
        step_history = task_history
        task = x['question']
        instruction = x['thoughts']

        if not check_instruction(task):
            print(task)
            continue
        
        # click - bbox
        # select - [click1, click2]
        # input - bbox + text

        x_act = []
        for i, xi in enumerate(act_list):
            action_type = xi['name'].lower()
            bbox = None
            point = None
            action_value = None
            
            if action_type in ['select_text']:
                start = xi['dual_point']['related']['from']
                start = re.findall(r'\d+\.\d+', start)
                start = [float(x) for x in start]
    
                end = xi['dual_point']['related']['to']
                end = re.findall(r'\d+\.\d+', end)
                end = [float(x) for x in end]
                point = [start, end]
            
            if action_type in ['click', 'input', 'select', 'hover']:
                bbox_str = xi['element']['related']
                bbox_val = re.findall(r'\d+\.\d+', bbox_str)
                bbox = [float(x) for x in bbox_val]
                # bbox = normalize_bbox(bbox, img_size)
                point = bbox_2_point(bbox)
                # img_draw = draw_point_bbox(img_url, point)
                # print(task)
                # display(img_draw)

            if action_type in ['input', 'select', 'answer', 'copy']:
                action_value = xi['text']

            if action_type in ['scroll']:
                down = float(xi['scroll']['related']['down'])
                right = float(xi['scroll']['related']['right'])
                if down > 0:
                    action_value = 'down'
                elif down < 0:
                    action_value = 'up'
                    
                if right > 0:
                    action_value = 'right'
                elif right < 0:
                    action_value = 'left'

            xi['action_type'] = action_type
            xi['action_value'] = action_value
            # if action_value is not None and not check_instruction(action_value):
            #     continue

            xi['point'] = point
            x_act.append(xi)

        task_now = {
            "id": "guiact_websingle_{}".format(total_i),
            "step_id": i,

            "task": task,
            "thought": instruction, 
            "img_url": img_uid,
            "img_size": img_size,

            "action_type": action_type,
            "action_value": action_value,
            "bbox": bbox,
            "point": point,
            
            "step": x_act,
            "step_history": step_history.copy(),
            }
        total_step.append(task_now)
        
        # step_history.append(xi)
        task_now_cp = task_now.copy()
        task_now_cp.pop('step_history')
        task_dict[task_id].append(task_now_cp)
        total_i += 1
#%%
save_url = f"{data_dir}/GUI_Course/GUIAct/metadata/hf_{args.split}_{args.stage}.json"

with open(save_url, "w") as file:
    json.dump(total_step, file, indent=4)
#%%
# mobile
args = parser.parse_args([])
args.stage = 'smartphone'
args.split = 'train'
# args.split = 'test'

args.web_json = f'{data_dir}/GUI_Course/GUIAct/metadata/{args.stage}_{args.split}_data.json'
web_train = json.load(open(args.web_json, "r"))
#%%
total_step = []
total_i = 0

past_group_id = None
for _, x in enumerate(tqdm(web_train)):
    if isinstance(x, dict):
        act_list = x['actions_label']
        img_uid = x['image_id']
        img_url = os.path.join(args.web_imgs, img_uid+'.png')                
        img_size = [x['image_size']['width'], x['image_size']['height']]
        
        task = x['question']
        instruction = x['thoughts']

        if not check_instruction(task):
            print(task)
            continue
        
        group_id = '_'.join(img_uid.split('_')[:-2])
        if group_id != past_group_id:
            step_history = []

        if True:
            xi = act_list
            action_type = xi['name'].lower()
            bbox = None
            point = None
            action_value = None
            
            if action_type in ['swipe']:
                start = xi['dual_point']['related']['from']
                start = re.findall(r'\d+\.\d+', start)
                start = [float(x) for x in start]
    
                end = xi['dual_point']['related']['to']
                end = re.findall(r'\d+\.\d+', end)
                end = [float(x) for x in end]
                point = [start, end]
            
            if action_type in ['tap']:
                point_str = xi['point']['related']
                point_val = re.findall(r'\d+\.\d+', point_str)
                point = [float(x) for x in point_val]
                
            if action_type in ['input', 'answer']:
                action_value = xi['text']

            xi['action_type'] = action_type
            xi['action_value'] = action_value
            # if action_value is not None and not check_instruction(action_value):
            #     continue
            
            xi['point'] = point
            xi['img_url'] = img_uid
            
            total_step.append({
                "id": "guiact_websingle_{}".format(total_i),
                "step_id": i,

                "task": task,
                "thought": instruction, 
                "img_url": img_uid,
                "img_size": img_size,

                "action_type": action_type,
                "action_value": action_value,
                "bbox": bbox,
                "point": point,
                
                "step": xi,
                "step_history": step_history.copy(),
                })
            step_history.append(xi)
            total_i += 1

            past_group_id = group_id
#%%
save_url = f"{data_dir}/GUI_Course/GUIAct/metadata/hf_{args.split}_{args.stage}.json"

with open(save_url, "w") as file:
    json.dump(total_step, file, indent=4)
#%%
